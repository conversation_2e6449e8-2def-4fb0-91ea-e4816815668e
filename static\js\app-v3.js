/**
 * AutoPilot AI V3.0 前端应用
 * 
 * 统一架构版本，支持：
 * 1. 两阶段意图分析（框架分析 + 偏好分析）
 * 2. ICP迭代规划流程
 * 3. 实时SSE事件流
 * 4. 透明化UI界面
 */

class AutoPilotAppV3 {
    constructor() {
        this.sessionId = null;
        this.userId = 1;
        this.eventSource = null;
        this.isPlanning = false;
        this.currentTaskId = null;
        
        // V3架构的阶段定义
        this.phases = {
            framework_analysis: {
                name: '核心框架分析',
                description: '分析旅行目的地、天数、主题等核心要素',
                completed: false,
                result: null
            },
            preference_analysis: {
                name: '个性化偏好分析', 
                description: '分析景点、美食、住宿等个性化偏好',
                completed: false,
                result: null
            },
            prepare_context: {
                name: '上下文准备',
                description: '整合分析结果，准备规划上下文',
                completed: false,
                result: null
            },
            icp_planning: {
                name: 'ICP迭代规划',
                description: '基于思考-行动-观察循环的智能规划',
                completed: false,
                result: null
            }
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.resetUI();
        console.log('AutoPilot AI V3.0 初始化完成');
    }
    
    bindEvents() {
        // 绑定表单提交事件
        const form = document.getElementById('planningForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startPlanning();
            });
        }
        
        // 绑定立即规划按钮
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startPlanning();
            });
        }
        
        // 绑定取消按钮
        const cancelBtn = document.getElementById('cancelPlanningBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 绑定视图切换按钮
        const listViewBtn = document.getElementById('viewModeList');
        const mapViewBtn = document.getElementById('viewModeMap');
        
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => {
                this.switchView('list');
            });
        }
        
        if (mapViewBtn) {
            mapViewBtn.addEventListener('click', () => {
                this.switchView('map');
            });
        }
    }
    
    async startPlanning() {
        if (this.isPlanning) {
            console.log('规划已在进行中');
            return;
        }
        
        const userQuery = document.getElementById('userQuery').value.trim();
        if (!userQuery) {
            alert('请输入您的旅行需求');
            return;
        }
        
        this.isPlanning = true;
        this.resetUI();
        this.showAnalysisView();
        
        try {
            // 生成会话ID
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            console.log('开始V3规划流程:', {
                sessionId: this.sessionId,
                userId: this.userId,
                query: userQuery
            });

            // 调用V3 API启动规划 - 直接处理SSE流
            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString(),
                    execution_mode: 'automatic'
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            // 处理SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('启动规划失败:', error);
            this.showError('启动规划失败: ' + error.message);
            this.isPlanning = false;
        }
    }

    async processSSEStream(response) {
        console.log('开始处理SSE流');

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.slice(6));
                            console.log('收到SSE事件:', eventData);
                            this.handleSSEEvent(eventData);
                        } catch (error) {
                            console.error('解析SSE事件失败:', error, line);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('处理SSE流失败:', error);
            this.showError('处理SSE流失败: ' + error.message);
        } finally {
            this.isPlanning = false;
        }
    }
    
    handleSSEEvent(data) {
        const { event, data: eventData, timestamp } = data;
        
        switch (event) {
            case 'start':
                this.handleStartEvent(eventData);
                break;
                
            case 'node_complete':
                this.handleNodeCompleteEvent(eventData);
                break;
                
            case 'complete':
                this.handleCompleteEvent(eventData);
                break;
                
            case 'error':
                this.handleErrorEvent(eventData);
                break;
                
            default:
                console.log('未知事件类型:', event, eventData);
        }
    }
    
    handleStartEvent(data) {
        console.log('规划开始:', data);
        this.updateAnalysisStatus('开始分析', '正在初始化AI分析流程...');
    }
    
    handleNodeCompleteEvent(data) {
        const { node_name, result } = data;
        console.log('节点完成:', node_name, result);
        
        // 根据节点名称更新对应的阶段
        if (node_name === 'framework_analysis') {
            this.phases.framework_analysis.completed = true;
            this.phases.framework_analysis.result = result.framework_analysis;
            this.updatePhaseStep('framework_analysis', 'completed');
        } else if (node_name === 'preference_analysis') {
            this.phases.preference_analysis.completed = true;
            this.phases.preference_analysis.result = result.preference_analysis;
            this.updatePhaseStep('preference_analysis', 'completed');
        } else if (node_name === 'prepare_context') {
            this.phases.prepare_context.completed = true;
            this.phases.prepare_context.result = result;
            this.updatePhaseStep('prepare_context', 'completed');
        } else if (node_name === 'icp_planning') {
            this.phases.icp_planning.completed = true;
            this.phases.icp_planning.result = result;
            this.updatePhaseStep('icp_planning', 'completed');
            
            // 显示最终行程
            if (result.final_itinerary) {
                this.showItinerary(result.final_itinerary);
            }
        }
    }
    
    handleCompleteEvent(data) {
        console.log('规划完成:', data);
        this.isPlanning = false;
        this.updateAnalysisStatus('规划完成', '您的个性化旅行行程已生成完毕');
        
        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }
    
    handleErrorEvent(data) {
        const { message, phase } = data;
        console.error('规划错误:', message, phase);
        this.showError(`规划过程中发生错误: ${message}`);
        this.isPlanning = false;

        if (phase && this.phases[phase]) {
            this.updatePhaseStep(phase, 'error');
        }
    }

    updateAnalysisStatus(title, description) {
        const titleEl = document.getElementById('analysisStatusTitle');
        const descEl = document.getElementById('analysisStatusDesc');

        if (titleEl) titleEl.textContent = title;
        if (descEl) descEl.textContent = description;
    }

    updatePhaseStep(phase, status) {
        // 动态创建或更新分析步骤
        const stepsContainer = document.getElementById('analysisSteps');
        if (!stepsContainer) return;

        let stepEl = document.getElementById(`step-${phase}`);

        if (!stepEl) {
            // 创建新的步骤元素
            stepEl = document.createElement('div');
            stepEl.id = `step-${phase}`;
            stepEl.className = 'analysis-step';
            stepEl.innerHTML = `
                <div class="step-indicator">
                    <div class="step-icon">
                        <i class="bi bi-circle"></i>
                    </div>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h6 class="step-title">${this.phases[phase].name}</h6>
                    <p class="step-description">${this.phases[phase].description}</p>
                    <div class="step-status">等待中...</div>
                </div>
            `;

            // 移除占位符
            const placeholder = stepsContainer.querySelector('.analysis-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            stepsContainer.appendChild(stepEl);
        }

        // 更新步骤状态
        const iconEl = stepEl.querySelector('.step-icon i');
        const statusEl = stepEl.querySelector('.step-status');

        stepEl.className = `analysis-step step-${status}`;

        switch (status) {
            case 'waiting':
                iconEl.className = 'bi bi-circle';
                statusEl.textContent = '等待中...';
                break;
            case 'running':
                iconEl.className = 'bi bi-arrow-clockwise spin';
                statusEl.textContent = '分析中...';
                break;
            case 'completed':
                iconEl.className = 'bi bi-check-circle-fill';
                statusEl.textContent = '已完成';
                break;
            case 'error':
                iconEl.className = 'bi bi-x-circle-fill';
                statusEl.textContent = '出错了';
                break;
        }
    }

    showAnalysisView() {
        // 隐藏等待视图，显示分析视图
        const waitingView = document.getElementById('waitingView');
        const analysisView = document.getElementById('analysisView');

        if (waitingView) waitingView.style.display = 'none';
        if (analysisView) analysisView.style.display = 'block';
    }

    showItinerary(itinerary) {
        console.log('显示行程:', itinerary);

        // 隐藏分析视图，显示行程视图
        const analysisView = document.getElementById('analysisView');
        const itineraryView = document.getElementById('itineraryView');

        if (analysisView) analysisView.style.display = 'none';
        if (itineraryView) itineraryView.style.display = 'block';

        // 更新行程信息
        this.updateItineraryDisplay(itinerary);
    }

    updateItineraryDisplay(itinerary) {
        // 更新行程标题和描述
        const titleEl = document.getElementById('itineraryTitle');
        const descEl = document.getElementById('itineraryDescription');

        if (titleEl && itinerary.itinerary_summary) {
            titleEl.textContent = itinerary.itinerary_summary.theme || '您的旅行行程';
        }

        if (descEl && itinerary.itinerary_summary) {
            const summary = itinerary.itinerary_summary;
            descEl.textContent = `${summary.destinations?.join('、') || ''} ${summary.total_days || 0}天行程`;
        }

        // 更新统计信息
        this.updateItineraryStats(itinerary);

        // 渲染每日行程
        this.renderDailyItinerary(itinerary);
    }

    updateItineraryStats(itinerary) {
        const totalDaysEl = document.getElementById('totalDays');
        const totalPOIsEl = document.getElementById('totalPOIs');
        const estimatedBudgetEl = document.getElementById('estimatedBudget');

        if (totalDaysEl && itinerary.itinerary_summary) {
            totalDaysEl.textContent = itinerary.itinerary_summary.total_days || 0;
        }

        // 计算总POI数量
        let totalPOIs = 0;
        if (itinerary.daily_plans) {
            Object.values(itinerary.daily_plans).forEach(dayPlan => {
                if (Array.isArray(dayPlan)) {
                    totalPOIs += dayPlan.length;
                }
            });
        }

        if (totalPOIsEl) {
            totalPOIsEl.textContent = totalPOIs;
        }

        // 这里可以添加预算计算逻辑
        if (estimatedBudgetEl) {
            estimatedBudgetEl.textContent = '¥待计算';
        }
    }

    renderDailyItinerary(itinerary) {
        // 这里可以渲染详细的每日行程
        console.log('渲染每日行程:', itinerary.daily_plans);
    }

    resetUI() {
        // 重置所有阶段状态
        Object.keys(this.phases).forEach(phase => {
            this.phases[phase].completed = false;
            this.phases[phase].result = null;
        });

        // 清空分析步骤容器
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                    </div>
                </div>
            `;
        }

        // 显示等待视图
        const waitingView = document.getElementById('waitingView');
        const analysisView = document.getElementById('analysisView');
        const itineraryView = document.getElementById('itineraryView');

        if (waitingView) waitingView.style.display = 'block';
        if (analysisView) analysisView.style.display = 'none';
        if (itineraryView) itineraryView.style.display = 'none';
    }

    showError(message) {
        console.error('显示错误:', message);
        alert(message); // 简单的错误显示，可以后续优化
    }

    cancelPlanning() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.isPlanning = false;
        this.resetUI();
        console.log('规划已取消');
    }

    switchView(viewType) {
        console.log('切换视图:', viewType);
        // 这里可以实现列表/地图视图切换
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.autoPilotApp = new AutoPilotAppV3();
});
