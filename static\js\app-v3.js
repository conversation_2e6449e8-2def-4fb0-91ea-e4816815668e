/**
 * AutoPilot AI 前端应用 V3.0 - 统一架构版
 * 
 * 支持新的两阶段工作流和事件驱动架构：
 * 1. 意图分析阶段：框架分析 + 偏好分析
 * 2. ICP迭代规划阶段：思考-行动-观察循环
 */

class AutoPilotAppV3 {
    constructor() {
        this.currentTaskId = null;
        this.eventSource = null;
        this.isPlanning = false;
        
        // 阶段状态跟踪
        this.phases = {
            framework_analysis: { completed: false, result: null },
            preference_analysis: { completed: false, result: null },
            prepare_context: { completed: false, result: null },
            icp_planning: { completed: false, result: null }
        };
        
        this.initializeUI();
        this.bindEvents();
    }
    
    initializeUI() {
        // 创建主要UI元素
        this.createProgressIndicator();
        this.createPhaseDisplay();
        this.createResultDisplay();
    }
    
    createProgressIndicator() {
        const progressContainer = document.getElementById('progress-container');
        if (!progressContainer) return;
        
        progressContainer.innerHTML = `
            <div class="progress-header">
                <h3>规划进度</h3>
                <div class="task-id" id="task-id-display"></div>
            </div>
            <div class="phase-progress">
                <div class="phase-step" data-phase="framework_analysis">
                    <div class="step-icon">1</div>
                    <div class="step-label">核心框架分析</div>
                    <div class="step-status">等待中</div>
                </div>
                <div class="phase-step" data-phase="preference_analysis">
                    <div class="step-icon">2</div>
                    <div class="step-label">个性化偏好分析</div>
                    <div class="step-status">等待中</div>
                </div>
                <div class="phase-step" data-phase="prepare_context">
                    <div class="step-icon">3</div>
                    <div class="step-label">规划上下文准备</div>
                    <div class="step-status">等待中</div>
                </div>
                <div class="phase-step" data-phase="icp_planning">
                    <div class="step-icon">4</div>
                    <div class="step-label">ICP迭代规划</div>
                    <div class="step-status">等待中</div>
                </div>
            </div>
        `;
    }
    
    createPhaseDisplay() {
        const phaseContainer = document.getElementById('phase-display');
        if (!phaseContainer) return;
        
        phaseContainer.innerHTML = `
            <div class="current-phase">
                <h4>当前阶段</h4>
                <div class="phase-info" id="current-phase-info">
                    <div class="phase-name">准备开始</div>
                    <div class="phase-description">等待用户输入...</div>
                </div>
            </div>
            <div class="phase-details" id="phase-details">
                <div class="thinking-log" id="thinking-log" style="display: none;">
                    <h5>AI思考过程</h5>
                    <div class="log-content"></div>
                </div>
                <div class="action-log" id="action-log" style="display: none;">
                    <h5>执行动作</h5>
                    <div class="log-content"></div>
                </div>
            </div>
        `;
    }
    
    createResultDisplay() {
        const resultContainer = document.getElementById('result-display');
        if (!resultContainer) return;
        
        resultContainer.innerHTML = `
            <div class="analysis-results" id="analysis-results" style="display: none;">
                <h4>分析结果</h4>
                <div class="framework-result" id="framework-result"></div>
                <div class="preference-result" id="preference-result"></div>
            </div>
            <div class="itinerary-display" id="itinerary-display" style="display: none;">
                <h4>规划行程</h4>
                <div class="daily-plans" id="daily-plans"></div>
            </div>
        `;
    }
    
    bindEvents() {
        // 绑定开始规划按钮 - 修正按钮ID
        const startButton = document.getElementById('planButton');
        if (startButton) {
            startButton.addEventListener('click', (e) => {
                e.preventDefault(); // 阻止表单提交
                this.startPlanning();
            });
        } else {
            console.error('找不到开始规划按钮: planButton');
        }

        // 绑定停止按钮
        const stopButton = document.getElementById('stop-planning');
        if (stopButton) {
            stopButton.addEventListener('click', () => this.stopPlanning());
        }
    }
    
    async startPlanning() {
        const userQuery = document.getElementById('userQuery')?.value;
        if (!userQuery) {
            alert('请输入您的旅行需求');
            return;
        }
        
        if (this.isPlanning) {
            console.log('规划已在进行中');
            return;
        }
        
        this.isPlanning = true;
        this.resetUI();
        
        try {
            // 准备请求数据
            const userId = document.getElementById('userId')?.value || '1';
            const requestData = {
                user_query: userQuery,
                user_id: parseInt(userId),
                execution_mode: 'automatic',
                user_profile: this.getUserProfile()
            };
            
            // 创建SSE连接
            this.eventSource = new EventSource('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            
            // 注意：由于浏览器限制，我们需要使用fetch + ReadableStream
            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // 处理SSE流
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.slice(6));
                            this.handleSSEEvent(eventData);
                        } catch (e) {
                            console.error('Failed to parse SSE event:', e);
                        }
                    }
                }
            }
            
        } catch (error) {
            console.error('Planning failed:', error);
            this.handleError(error.message);
        } finally {
            this.isPlanning = false;
        }
    }
    
    handleSSEEvent(event) {
        console.log('Received event:', event);
        
        switch (event.event) {
            case 'start':
                this.handleStartEvent(event.data);
                break;
            case 'node_complete':
                this.handleNodeComplete(event.data);
                break;
            case 'phase_start':
                this.handlePhaseStart(event.data);
                break;
            case 'phase_end':
                this.handlePhaseEnd(event.data);
                break;
            case 'PLANNING_LOG':
                this.handlePlanningLog(event.data);
                break;
            case 'ITINERARY_UPDATE':
                this.handleItineraryUpdate(event.data);
                break;
            case 'complete':
                this.handleComplete(event.data);
                break;
            case 'error':
                this.handleError(event.data.message);
                break;
            case 'eos':
                this.handleEndOfStream();
                break;
            default:
                console.log('Unknown event type:', event.event);
        }
    }
    
    handleStartEvent(data) {
        this.currentTaskId = data.task_id;
        document.getElementById('task-id-display').textContent = `任务ID: ${this.currentTaskId}`;
        this.updateCurrentPhase('开始规划', '正在初始化规划流程...');
    }
    
    handleNodeComplete(data) {
        const { node_name, result } = data;
        
        // 更新阶段状态
        if (this.phases[node_name]) {
            this.phases[node_name].completed = true;
            this.phases[node_name].result = result;
            this.updatePhaseStep(node_name, 'completed');
        }
        
        // 显示阶段结果
        this.displayPhaseResult(node_name, result);
    }
    
    handlePhaseStart(data) {
        const { phase_name, title, message } = data;
        this.updateCurrentPhase(title, message);
        
        if (this.phases[phase_name]) {
            this.updatePhaseStep(phase_name, 'running');
        }
    }
    
    handlePhaseEnd(data) {
        const { phase_name, status, result } = data;
        
        if (this.phases[phase_name]) {
            this.updatePhaseStep(phase_name, status === 'success' ? 'completed' : 'error');
        }
    }
    
    handlePlanningLog(data) {
        const { message, reasoning_step } = data;
        this.addThinkingLog(`步骤 ${reasoning_step}: ${message}`);
    }
    
    handleItineraryUpdate(data) {
        const { day, activity } = data;
        this.addItineraryItem(day, activity);
    }
    
    handleComplete(data) {
        this.updateCurrentPhase('规划完成', '您的旅行规划已经完成！');
        this.displayFinalResult(data);
    }
    
    handleError(message) {
        this.updateCurrentPhase('发生错误', message);
        console.error('Planning error:', message);
    }
    
    handleEndOfStream() {
        console.log('SSE stream ended');
        this.isPlanning = false;
    }
    
    updatePhaseStep(phaseName, status) {
        const stepElement = document.querySelector(`[data-phase="${phaseName}"]`);
        if (!stepElement) return;
        
        const statusElement = stepElement.querySelector('.step-status');
        const iconElement = stepElement.querySelector('.step-icon');
        
        stepElement.className = `phase-step ${status}`;
        
        switch (status) {
            case 'running':
                statusElement.textContent = '进行中...';
                iconElement.innerHTML = '<div class="spinner"></div>';
                break;
            case 'completed':
                statusElement.textContent = '已完成';
                iconElement.innerHTML = '✓';
                break;
            case 'error':
                statusElement.textContent = '出错';
                iconElement.innerHTML = '✗';
                break;
            default:
                statusElement.textContent = '等待中';
        }
    }
    
    updateCurrentPhase(name, description) {
        const phaseInfo = document.getElementById('current-phase-info');
        if (phaseInfo) {
            phaseInfo.innerHTML = `
                <div class="phase-name">${name}</div>
                <div class="phase-description">${description}</div>
            `;
        }
    }
    
    addThinkingLog(message) {
        const thinkingLog = document.getElementById('thinking-log');
        if (thinkingLog) {
            thinkingLog.style.display = 'block';
            const logContent = thinkingLog.querySelector('.log-content');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = message;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }
    }
    
    addItineraryItem(day, activity) {
        const dailyPlans = document.getElementById('daily-plans');
        if (!dailyPlans) return;
        
        document.getElementById('itinerary-display').style.display = 'block';
        
        let dayContainer = dailyPlans.querySelector(`[data-day="${day}"]`);
        if (!dayContainer) {
            dayContainer = document.createElement('div');
            dayContainer.className = 'day-plan';
            dayContainer.setAttribute('data-day', day);
            dayContainer.innerHTML = `
                <h5>第 ${day} 天</h5>
                <div class="activities"></div>
            `;
            dailyPlans.appendChild(dayContainer);
        }
        
        const activitiesContainer = dayContainer.querySelector('.activities');
        const activityElement = document.createElement('div');
        activityElement.className = 'activity-item';
        activityElement.innerHTML = `
            <div class="activity-name">${activity.name}</div>
            <div class="activity-details">${activity.address || ''}</div>
        `;
        activitiesContainer.appendChild(activityElement);
    }
    
    displayPhaseResult(phaseName, result) {
        if (phaseName === 'framework_analysis' || phaseName === 'preference_analysis') {
            document.getElementById('analysis-results').style.display = 'block';
            
            if (phaseName === 'framework_analysis') {
                this.displayFrameworkResult(result);
            } else if (phaseName === 'preference_analysis') {
                this.displayPreferenceResult(result);
            }
        }
    }
    
    displayFrameworkResult(result) {
        const container = document.getElementById('framework-result');
        if (!container || !result.framework_analysis) return;
        
        const coreIntent = result.framework_analysis.core_intent;
        container.innerHTML = `
            <h5>核心框架分析</h5>
            <div class="result-item">
                <strong>目的地：</strong>${coreIntent.destinations?.join(', ') || '未知'}
            </div>
            <div class="result-item">
                <strong>天数：</strong>${coreIntent.travel_days || '未知'}
            </div>
            <div class="result-item">
                <strong>主题：</strong>${coreIntent.travel_theme?.join(', ') || '未知'}
            </div>
            <div class="result-item">
                <strong>预算：</strong>${coreIntent.budget_range || '未知'}
            </div>
        `;
    }
    
    displayPreferenceResult(result) {
        const container = document.getElementById('preference-result');
        if (!container || !result.preference_analysis) return;
        
        const attractions = result.preference_analysis.attraction_preferences;
        container.innerHTML = `
            <h5>偏好分析</h5>
            <div class="result-item">
                <strong>偏好景点类型：</strong>${attractions.preferred_types?.join(', ') || '未知'}
            </div>
            <div class="result-item">
                <strong>必游景点：</strong>${attractions.must_visit?.join(', ') || '无'}
            </div>
        `;
    }
    
    displayFinalResult(data) {
        // 显示最终结果
        console.log('Final result:', data);
    }
    
    getUserProfile() {
        // 从表单或本地存储获取用户画像
        return {
            age: 30,
            interests: ['文化', '美食'],
            budget_level: '中等'
        };
    }
    
    resetUI() {
        // 重置UI状态
        Object.keys(this.phases).forEach(phase => {
            this.phases[phase].completed = false;
            this.phases[phase].result = null;
            this.updatePhaseStep(phase, 'waiting');
        });
        
        document.getElementById('analysis-results').style.display = 'none';
        document.getElementById('itinerary-display').style.display = 'none';
        document.getElementById('thinking-log').style.display = 'none';
        
        const logContents = document.querySelectorAll('.log-content');
        logContents.forEach(content => content.innerHTML = '');
        
        const dailyPlans = document.getElementById('daily-plans');
        if (dailyPlans) dailyPlans.innerHTML = '';
    }
    
    stopPlanning() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isPlanning = false;
        this.updateCurrentPhase('已停止', '规划已被用户停止');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.autoPilotApp = new AutoPilotAppV3();
});
