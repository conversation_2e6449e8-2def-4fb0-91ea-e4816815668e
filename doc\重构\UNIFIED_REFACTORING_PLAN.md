# AutoPilot AI 统一架构重构开发计划 (V3.0)

## 1. 概述 (Executive Summary)

本文档旨在为AutoPilot AI项目提供一份全面的、统一的架构重构开发计划。计划的核心目标是将四个独立的重构设想（`意图整合`、`规划距离逻辑`、`推送`、`旅游搭子UI`）融合成一个单一、内聚、事件驱动的现代化AI架构。

重构后的系统将由AI在`LangGraph`中主导，通过一个“思考 -> 行动 -> 观察”的迭代循环（ICP模型）动态构建行程，并通过一个强大的事件总线将每一步进展实时推送给一个全新的、透明化的前端界面，从而在提升智能水平、优化用户体验和增强系统可维护性之间达到最佳平衡。

## 2. 三大核心架构支柱 (Core Architectural Pillars)

新架构建立在三个紧密协作的核心组件之上，它们是实现系统统一性的基石。

### 2.1. `StandardAgentState`：统一状态管理

-   **角色**: 这是整个`LangGraph`工作流中流动的**单一事实来源 (Single Source of Truth)**。
-   **实现文件**: `src/agents/travel_planner_lg/state.py`
-   **修改方式**: **重写 (Rewrite)**。使用一个新的、更全面的 `StandardAgentState` TypedDict 定义，完全取代旧的 `TravelPlanState`。
-   **核心字段映射**:
    -   `framework_analysis`, `preference_analysis`: 承载 **意图整合** 阶段的产出。
    -   `planning_log`, `daily_plans`, `current_action`: 记录 **ICP迭代规划** 的过程与结果。
    -   `task_id`, `current_phase`, `notification_service`: 支持 **事件推送** 架构的核心字段。
    -   整个`StandardAgentState`的JSON序列化版本，是 **UI界面** 所需数据的完整来源。

### 2.2. `UnifiedToolRegistry`：统一工具注册与调用

-   **角色**: 一个中央工具注册表，负责管理Agent可以调用的所有“能力”。
-   **实现文件**: `src/tools/unified_registry.py`
-   **修改方式**: **新建 (Create)**。创建一个新的文件来实现 `UnifiedToolRegistry` 类，并导出一个全局可用的单例。
-   **核心设计：职责分离**
    1.  **Action Tools**: 负责与外部世界交互（如API调用、数据库查询）。通过`@unified_registry.register_action_tool`装饰器注册。
    2.  **Planner Tools**: 负责在Agent内部辅助思考（如格式化Prompt、整合数据）。通过`@unified_registry.register_planner_tool`装饰器注册。

### 2.3. `UnifiedEventBus`：统一事件总线

-   **角色**: 基于Redis实现的、负责解耦和实时通信的事件中心。
-   **实现文件**: `src/services/unified_event_bus.py`
-   **修改方式**: **新建 (Create)**。创建一个新的文件来封装所有与Redis Pub/Sub和HASH操作相关的逻辑。
-   **核心设计：双重职责**
    1.  **消息总线 (Redis Pub/Sub)**: Agent的任何关键进展（阶段开始/结束、工具调用/完成）都会作为事件发布到此，供任何下游系统（如SSE推送服务）订阅。
    2.  **实时作战室 (Redis HASH)**: 为每个规划任务在Redis中维护一个与`StandardAgentState`实时同步的HASH。这为系统提供了极佳的可观测性、调试能力，并为未来的任务恢复、状态监控等高级功能奠定了基础。

## 3. 端到端重构工作流 (End-to-End Refactored Workflow)

重构后的系统将遵循一个清晰的、由事件驱动的两阶段工作流。

**实现文件**:
-   **节点定义**: `src/agents/travel_planner_lg/nodes.py` (**重构/新建**)
-   **图构建**: `src/agents/travel_planner_lg/graph.py` (**重构**)

![Workflow Diagram](https://mermaid.ink/svg/pako:eNqNVMFu2zAM_RVC5yApgsZuswAdhg5buhVFigxJGiwFlgyJ7CAQ_PcpJSVbBsewBwmJv-Rj4pPoJIdO6WJpQZ_oM58Vq00n2d20y00-a6yY5p92lYgGzQkM6_vR9I_d-W587k49DqLh75_x2sQ68wYk2mEclz58H5iYw2wz-n3J2R36S0rVvM-xJkHw-tP8u-z0-jP6c_G7Xm31hL7zM-kCq7Qd64vF3-7kQ22S84R9S99c15y_TqJ8-0QdD0I8r9J0Xg-5gT-j5L1-yW5zNnEOTyJ8-S9jG4_g2vP7a8x3Tq56_eYk-t_6k2v73G9K_U-iG4H2H0S_XzUqK38y0pY92lM2N2fD7fH5c49k-2y3g_hT660eD4y2P40o-X9D5-y3vX6P7L2mH_K2nN0lXqHqS6P0R5l79H5Fq3uP7m-lW2K4-jC5f2l5fHn4V5l7-H-3e5-w_B1d26yD3H8b2u9-fT69j6g-p6U3K05yE6e5N6t2u5X2t2uP7m4yHq9J5M7Tq173n9q-H1fP7bO6W6M8wD19fX87n6-lP_T_d5-V-7a9wT23WqS64b1-g-m0G6u1U6e6N-g-h-hO1-jP4-L6j_J-3a-g_hH4H6GfE_lPtfv9eT9_W_34a-r5aVjHlS9j9iP4P4B-p9q1-v-o-W_hK4H6B-h-5X-D_kfa_Tz_Wf4P-j61-u_K_f_Xv8-9P829-q-g-D8j_of6r9-v0c_v0D_f4B-v8D_S83_f_o_8-N9D_M_w_kP_A_0v8l__8q_y_G_0_1_2V__yp_v_T_d__9_P77Bf3_N_B_qP6H_P_G__828H_k_0P9j-L_X8X_fw_8H_r_oP5H_f9G__828H_k_0P9j-L_X8X_fw_8H_r_oP5H_f9G__828H_k_0P9j-L_X8X_fw_8H_r_oP5H__9m_P8f_n-s__9s4P_I_w_1P4r_fxX__zfwf-j_g-of9f8b__9t4P_I_w_1P4r_fxX__zfwf-j_g-of9f8b__9t4P_I_w_1P4r_fxX__zfwf-j_g-of_f-Z8f9_-P-x_v-zgeVd)

### **阶段一：意图分析 (Intent Analysis)**

1.  **触发**: 用户提交查询，API层创建一个唯一的`task_id`，并启动后台`LangGraph`工作流。
2.  **执行**:
    -   `framework_analyzer`节点运行，对用户查询进行核心框架分析。
    -   `UnifiedEventBus`发布`phase_start(framework_analysis)`事件。
    -   分析完成后，结果存入`StandardAgentState.framework_analysis`。
    -   `UnifiedEventBus`发布`phase_end(framework_analysis)`事件。
    -   `preference_analyzer`节点运行，在前一阶段的基础上进行个性化偏好分析。
    -   `UnifiedEventBus`发布`phase_start(preference_analysis)`事件。
    -   分析完成后，结果存入`StandardAgentState.preference_analysis`。
    -   `UnifiedEventBus`发布`phase_end(preference_analysis)`事件。
3.  **UI表现**: 前端左侧的“意图分析面板”根据收到的`phase_start`/`phase_end`事件，实时更新每个分析项的状态（等待中 -> 分析中 -> 已完成）和结果。

### **阶段二：迭代式上下文规划 (ICP Planning)**

1.  **触发**: 意图分析完成后，`context_preparer`节点整合分析结果，工作流自动进入ICP规划循环。
2.  **执行 (循环)**:
    -   **思考 (Think)**: `planner_agent`节点基于当前的`StandardAgentState`，决定下一步要采取的行动，并生成一个包含`tool_name`和`parameters`的指令。同时，将思考过程记录到`planning_log`。
    -   **行动 (Act)**: `tool_executor`节点根据指令，通过`UnifiedToolRegistry`调用对应的`Action Tool`。
    -   **观察 (Observe)**: 工具执行的结果被写回`StandardAgentState.tool_results`，完成一次循环。控制权交还给`planner_agent`进行下一步思考。
3.  **事件与UI表现**:
    -   `planner_agent`的每一次思考，都会触发`PLANNING_LOG`事件，在UI右侧面板显示为一条“思考日志”。
    -   `tool_executor`调用工具前后，`UnifiedToolRegistry`会自动发布`tool_start`和`tool_end`事件，在UI右侧显示为一条“工具调用日志”。
    -   当`planner_agent`认为部分行程可以确定时，会调用内部工具更新`daily_plans`，并触发`ITINERARY_UPDATE`事件，在UI右侧实时追加“POI信息卡片”。
    -   循环直至`planner_agent`发出`finish_planning`指令，触发`complete`事件，流程结束。

## 4. 代码复用与重构策略 (Code Reuse & Refactoring Strategy)

我们将最大化复用现有代码，仅在必要时进行重构或封装。

-   **`tools/Amap/map_tool.py`**:
    -   **文件位置**: `tools/Amap/map_tool.py`
    -   **复用方式**: **非侵入式复用**。无需修改任何函数内部逻辑，仅需在目标函数（如 `search_pois`, `get_route`）的定义上方，**添加一行`@unified_registry.register_action_tool`装饰器**，即可将其无缝接入新架构。

-   **`src/agents/services/**.py` (如 `amap_service.py`)**:
    -   **文件位置**: `src/agents/services/`
    -   **复用方式**: 同上。将其类方法用`@unified_registry.register_action_tool`装饰，成为Agent可用的标准工具。

-   **`src/models/mysql_*.py`**:
    -   **文件位置**: `src/models/`
    -   **复用方式**: **完全复用**。这些模型和CRUD操作是获取用户画像、历史数据等关键信息的基础，将在意图分析阶段被调用，为Agent提供决策依据。

-   **`src/prompts/`**:
    -   **重构方式**: **采用“先建新，后废旧”的安全策略**。首先，在`src/prompts/travel_planner/`下**新建**`consolidated/`子目录及`01_framework_analyzer.md`、`02_preference_analyzer.md`和`planner_agent.md`这三个新的Prompt文件。待新架构完全测试稳定后，再安全地**删除**旧的六个分散的Prompt文件。

-   **`static/`**:
    -   **重构方式**: 根据`旅游搭子UI.md`的设计，创建`static/js/components/`目录，将UI拆分为`IntentAnalysisPanel.js`、`PlanningPanel.js`等组件。现有的CSS样式和JS库（如`axios`）可被复用。主入口文件将是新的`app-v3.js`。

### 4.5. 数据库集成深度策略 (In-Depth Database Integration Strategy)

数据库在新架构中扮演四个关键角色，贯穿从意图理解到记忆沉淀的全过程。

#### a. 作为规划的“初始上下文” (As Initial Context for Planning)

-   **集成点**: 在工作流的**第一阶段（意图分析）**开始之前。
-   **实现方式**: 在`src/agents/travel_planner_lg/nodes.py`的`run_framework_analysis`节点函数内部，应首先通过`UserProfileService`或相应的`mysql_crud`方法，根据`user_id`从MySQL中拉取用户的完整画像。
    -   `dh_user_profile.user_profiles`: 用户的基本偏好、旅行风格标签（如“经济型”、“亲子游”）。
    -   `dh_user_profile.user_memories`: 过去行程中沉淀的记忆，例如“用户不喜欢辛辣食物”、“用户对历史博物馆有浓厚兴趣”。
    -    测试的user_id=1
-   **价值**: 将这些富含信息的上下文与用户的原始查询一同提供给LLM，使其在第一步就能做出高度个性化的分析，而不是进行冷冰冰的通用分析。

#### b. 作为Agent可动态调用的“行动工具” (As Dynamic Action Tools for the Agent)

-   **集成点**: 在工作流的**第二阶段（ICP迭代规划）**中。
-   **实现方式**: 在`src/tools/`下**新建**一个`database_tools.py`文件，用于实现数据库相关的`Action Tool`（例如`get_user_past_stays`），并使用`@unified_registry.register_action_tool`进行装饰。
-   **价值**: 赋予Agent在规划过程中动态查询数据库的能力。例如，当Agent规划到“北京”的住宿时，它可以主动调用`get_user_past_stays`工具，查询用户过去在北京住过哪些酒店及其评价，从而做出更智能的推荐。

#### c. 作为规划结果的“最终归宿” (As the Final Destination for Results)

-   **集成点**: 在`LangGraph`工作流的**终点**。
-   **实现方式**: 在`src/agents/travel_planner_lg/nodes.py`中**新建**一个`save_itinerary_node`节点函数，并将其添加到计算图的末端。该节点负责从`StandardAgentState`中提取最终行程，并调用`mysql_crud`将其写入数据库。
-   **价值**: 确保每一次成功的规划都有结构化的、可供未来查询和分析的持久化记录。

#### d. 作为“记忆沉淀与画像更新”的载体 (As the Foundation for Memory & Profile Updates)

-   **集成点**: 在`save_itinerary_node`之后，或作为后台异步任务。
-   **实现方式**: 在`src/agents/travel_planner_lg/nodes.py`中**新建**一个`consolidate_memory_node`节点函数。该节点负责分析本次规划全流程，调用LLM提取有价值的新记忆点，并更新到用户记忆库中。
-   **价值**: 这构成了系统学习和进化的**核心闭环**。Agent的能力会随着与用户的每一次成功交互而成长，未来的规划也会因此变得越来越“懂你”。

## 5. 外部依赖集成规范 (External Dependencies Integration)

-   **最佳实践**: 当需要集成新的外部服务（如酒店预订、机票查询API）时，**严禁直接手写`httpx/requests`调用**。
-   **标准流程**:
    1.  使用 **Context7** 搜索该服务是否存在官方或高质量的第三方Python SDK。
    2.  优先选择并集成该SDK。
    3.  将SDK的调用逻辑封装成一个新的`Action Tool`，并使用`@unified_registry.register_action_tool`进行注册。

## 6. 开发路线图 (Development Roadmap)

建议将重构工作分为五个连续的阶段，以确保平稳过渡和风险可控。

-   **阶段一：基础架构搭建 (Foundation)**
    -   [ ] 实现`UnifiedEventBus`服务（`src/services/unified_event_bus.py`）。
    -   [ ] 实现`UnifiedToolRegistry`类（`src/tools/unified_registry.py`）。
    -   [ ] 定义最终的`StandardAgentState` TypedDict（`src/agents/travel_planner_lg/state.py`）。
    -   [ ] **验证**: 对`tools/Amap/map_tool.py`中的函数进行装饰器封装，并编写单元测试验证工具注册与执行。

-   **阶段二：意图分析流程实现 (Intent Pipeline)**
    -   [ ] 在`src/prompts/travel_planner/consolidated/`下**新建**`01_framework_analyzer.md`和`02_preference_analyzer.md`两个新的Prompt。
    -   [ ] 在`src/agents/travel_planner_lg/nodes.py`中**新建**`run_framework_analysis`和`run_preference_analysis`两个新节点。
    -   [ ] 在`nodes.py`中**新建**`context_preparer`节点，用于整合分析结果。
    -   [ ] **验证**: 编写集成测试，验证两步分析流程能正确执行并填充`StandardAgentState`。

-   **阶段三：ICP规划核心实现 (ICP Core)**
    -   [ ] 在`src/prompts/travel_planner/consolidated/`下**新建**核心的`planner_agent.md` Prompt。
    -   [ ] 在`src/agents/travel_planner_lg/nodes.py`中**新建**`planner_agent_node`、`tool_executor_node`以及路由逻辑。
    -   [ ] 在`src/agents/travel_planner_lg/graph.py`中**重构**计算图，搭建包含ICP循环的新图。
    -   [ ] **验证**: 编写集成测试，模拟ICP循环，确保Agent能正确调用工具并逐步构建行程。

-   **阶段四：API与前端重构 (API & Frontend)**
    -   [ ] 在`src/api/travel_planner.py`中实现新的V3 SSE流式API端点。
    -   [ ] 按照组件化方案重构前端JS代码 (`app-v3.js`及`components/`目录)。
    -   [ ] 实现前端`SSEService`，用于处理来自后端的事件流。
    -   [ ] **验证**: 联调前后端，确保前端能正确接收并渲染所有类型的SSE事件。

-   **阶段五：集成、测试与交付 (Integration & Delivery)**
    -   [ ] 进行完整的端到端（E2E）测试，覆盖真实的用户场景。
    -   [ ] 修复所有发现的Bug。
    -   [ ] 安全地**删除**旧的六个Prompt文件和`nodes.py`中不再使用的旧节点函数。
    -   [ ] 交付重构后的系统。

## 7. 结论 (Conclusion)

这份统一重构计划将引领AutoPilot AI项目从一个多模块拼接的系统，演进为一个设计优雅、高度内聚、真正由事件驱动的智能化平台。通过本次重构，项目的代码质量、可维护性、可扩展性以及最终的用户体验都将迈上一个新的台阶。 